<template>
  <view class="nav-item" @click="handleNavigation">
    <view class="item-title">{{ title }}</view>
    <view class="item-container">
      <view class="uni-input">
        <text class="item-value" :class="{ placeholder: !value }">{{ value || placeholder }}</text>
        <!--  #ifdef  MP-WEIXIN -->
        <uni-icons type="arrowright" size="22" color="#fff"></uni-icons>
        <!--  #endif -->
        <!--  #ifndef  MP-WEIXIN -->
        <uni-icons type="arrowright" size="22" color="#999"></uni-icons>
        <!--  #endif -->

      </view>
    </view>
  </view>
</template>

<script setup>
// 定义组件接收的属性
const props = defineProps({
  // 标题文本
  title: {
    type: String,
    default: '选项名称'
  },
  // 当前显示的值
  value: {
    type: String,
    default: ''
  },
  // 未选择时的占位文本
  placeholder: {
    type: String,
    default: '请选择'
  },
});
// 定义事件
const emit = defineEmits(['click']);
// 处理导航
const handleNavigation = () => {
  emit('click');
};
</script>

<style lang="scss" scoped>
.nav-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 12rpx;
  min-height: 100rpx;
  padding: 20rpx;
  margin: 0 20rpx 20rpx;

  /*  #ifdef MP-WEIXIN  */
  background-color: #1A191E;
  /*  #endif  */
  /*  #ifndef MP-WEIXIN  */
  background-color: #FFFFFF;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  /*  #endif  */

  .item-title {
    font-size: 30rpx;
    /*  #ifdef MP-WEIXIN  */
    color: #E3E2E7;
    /*  #endif  */
    /*  #ifndef MP-WEIXIN  */
    color: #333;
    /*  #endif  */
  }

  .item-value {
    font-size: 28rpx;
    margin-right: 10rpx;
    max-width: 300rpx;
    text-align: right;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;

    /*  #ifdef MP-WEIXIN  */
    color: #E3E2E7;
    /*  #endif  */
    /*  #ifndef MP-WEIXIN  */
    color: #666;
    /*  #endif  */
    
    &.placeholder {
      /*  #ifdef MP-WEIXIN  */
      color: #888;
      /*  #endif  */
      /*  #ifndef MP-WEIXIN  */
      color: #999;
      /*  #endif  */
    }
  }

  .uni-input {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}
</style>