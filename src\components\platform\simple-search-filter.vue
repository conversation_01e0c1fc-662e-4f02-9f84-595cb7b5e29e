<template>
  <view class="search-filter-wrapper">

    <!-- 时间筛选 -->
    <view class="time-filter-section" v-if="showTimeFilter">
      <view class="time-filter-title">时间筛选：</view>
      <view class="time-filter-content">
        <view class="time-item">
          <up-datetime-picker
            v-model="startTimeValue"
            mode="date"
            placeholder='选择开始时间'
            @confirm="handleStartTimeChange"
            hasInput
            :minDate="minDate"
            :maxDate="maxDate"
            :inputProps="{
              border: 'surround',
              shape: 'square',
              inputAlign: 'center',
              suffixIcon: 'calendar'
            }"
          >
          </up-datetime-picker>
        </view>
        <view class="time-item">
          <up-datetime-picker
            v-model="endTimeValue"
            mode="date"
            placeholder='选择结束时间'
            @confirm="handleEndTimeChange"
            hasInput
            :minDate="minDate"
            :maxDate="maxDate"
            :inputProps="{
              border: 'surround',
              shape: 'square',
              inputAlign: 'center',
              suffixIcon: 'calendar'
            }"
          >
          </up-datetime-picker>
        </view>
      </view>
    </view>

    <!-- 快捷筛选标签 -->
    <view class="filter-section" v-if="filterTags.length > 0">
      <view class="filter-title">状态筛选：</view>
      <scroll-view class="filter-scroll" scroll-x="true" show-scrollbar="false">
        <view class="filter-tags">
          <view
            v-for="(tag, index) in filterTags"
            :key="index"
            class="filter-tag"
            :class="{ active: activeFilter === tag.value }"
            @click="handleFilterClick(tag.value)"
          >
            {{ tag.label }}
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 搜索结果统计 -->
    <view class="result-section" v-if="showResultCount">
      <text class="result-text">共 {{ resultCount }} 个结果</text>
      <view class="clear-btn" v-if="hasActiveFilters" @click="handleClearAll">
        <text class="clear-text">清空</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, watch, defineProps, defineEmits } from 'vue'

// Props
const props = defineProps({
  // 筛选标签
  filterTags: {
    type: Array,
    default: () => []
  },
  // 当前激活的筛选
  activeFilter: {
    type: [String, Number],
    default: ''
  },
  // 是否显示结果统计
  showResultCount: {
    type: Boolean,
    default: false
  },
  // 结果数量
  resultCount: {
    type: Number,
    default: 0
  },
  // 是否显示时间筛选
  showTimeFilter: {
    type: Boolean,
    default: false
  },
})

// Emits
const emit = defineEmits([
  'update:activeFilter',
  'search',
  'clear-all'
])

// 响应式数据
const currentFilter = ref(props.activeFilter)
const startTimeValue = ref(props.startTime)
const endTimeValue = ref(props.endTime)

// 计算最小日期（3年前）和最大日期（5年后）
const minDate = computed(() => {
  const date = new Date()
  date.setFullYear(date.getFullYear() - 3)
  return date.getTime()
})

const maxDate = computed(() => {
  const date = new Date()
  date.setFullYear(date.getFullYear() + 5)
  return date.getTime()
})

// 计算是否有激活的筛选条件
const hasActiveFilters = computed(() => {
  return currentFilter.value !== '' ||
         startTimeValue.value !== '' ||
         endTimeValue.value !== ''
})

// 监听内部值变化，同步到外部
watch(currentFilter, (newVal) => {
  emit('update:activeFilter', newVal)
})

// 筛选处理
const handleFilterClick = (value) => {
  // 如果点击的是当前激活的筛选，则取消选择
  currentFilter.value = currentFilter.value === value ? '' : value
  emitSearchEvent()
}

// 时间处理函数
const handleStartTimeChange = (value) => {
  if (value && value.value) {
    startTimeValue.value = value.value
    emitSearchEvent()
  }
}

const handleEndTimeChange = (value) => {
  if (value && value.value) {
    endTimeValue.value = value.value
    emitSearchEvent()
  }
}



// 格式化时间为 UTC 格式
const formatToUTC = (timestamp) => {
  if (!timestamp) return ''

  try {
    const date = new Date(timestamp)
    if (isNaN(date.getTime())) return ''
    return date.toISOString().replace('.000Z', 'Z')
  } catch (error) {
    return ''
  }
}

// 清空所有筛选
const handleClearAll = () => {
  currentFilter.value = ''
  startTimeValue.value = ''
  endTimeValue.value = ''
  emit('clear-all')
  emitSearchEvent()
}

// 发送搜索事件
const emitSearchEvent = () => {
  emit('search', {
    filter: currentFilter.value,
    startTime: startTimeValue.value ? formatToUTC(startTimeValue.value) : '',
    endTime: endTimeValue.value ? formatToUTC(endTimeValue.value) : ''
  })
}
</script>

<style lang="scss" scoped>
.search-filter-wrapper {
  background-color: #fff;
  padding: 20rpx 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

  .time-filter-section {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    overflow: hidden;

    .time-filter-title {
      font-size: 28rpx;
      color: #666;
      margin-right: 16rpx;
      white-space: nowrap;
      flex-shrink: 0;
    }

    .time-filter-content {
      display: flex;
      gap: 20rpx;
      flex: 1;

      .time-item {
        flex: 1;
      }
    }
  }

  .filter-section {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
    overflow: hidden;

    .filter-title {
      font-size: 28rpx;
      color: #666;
      margin-right: 16rpx;
      white-space: nowrap;
      flex-shrink: 0;
    }

    .filter-scroll {
      flex: 1;
      overflow-x: auto;
      
      .filter-tags {
        display: flex;
        gap: 12rpx;
        padding-right: 20rpx;
        min-width: max-content;

        .filter-tag {
          padding: 12rpx 20rpx;
          background-color: #f5f5f5;
          border-radius: 32rpx;
          font-size: 24rpx;
          color: #666;
          white-space: nowrap;
          transition: all 0.3s ease;
          cursor: pointer;
          min-width: fit-content;
          flex-shrink: 0;

          &.active {
            background-color: #667eea;
            color: #fff;
          }

          &:active {
            transform: scale(0.95);
          }
        }
      }
    }
  }

  .result-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 16rpx;
    border-top: 1rpx solid #f0f0f0;

    .result-text {
      font-size: 26rpx;
      color: #999;
    }

    .clear-btn {
      .clear-text {
        font-size: 26rpx;
        color: #667eea;
        cursor: pointer;

        &:active {
          color: #5a6fd8;
        }
      }
    }
  }
}
</style>
