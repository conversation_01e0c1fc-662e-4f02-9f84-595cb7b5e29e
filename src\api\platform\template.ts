/**
 * 模版先关的方法
 */

import config from '@/config';
import { getToken } from '@/utils/auth';
import request from '@/utils/request';


export interface ProjectListParams {
  pageNum?: number;
  pageSize?: number;
}

export interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}



export function listTemplates(params: ProjectListParams): Promise<ApiResponse<any>> {
  return request({
    url: '/video/template/list',
    method: 'GET',
  });

}