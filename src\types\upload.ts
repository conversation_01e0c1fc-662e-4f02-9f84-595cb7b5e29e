/**
 * 上传相关的类型定义
 */

// uview-plus 上传组件实例类型
export interface UploadInstance {
  chooseFile: () => void
  [key: string]: any
}

// 视频文件对象类型
export interface VideoFile {
  name: string          // 文件名
  size: number          // 文件大小（字节）
  thumb?: string        // 缩略图URL
  url?: string          // 文件URL
  type?: string         // 文件类型
  path?: string         // 文件路径
  duration?: number     // 视频时长（秒）
  width?: number        // 视频宽度
  height?: number       // 视频高度
  [key: string]: any    // 其他属性
}

// 上传进度回调参数
export interface UploadProgressEvent {
  progress: number      // 进度百分比 (0-100)
  loaded: number        // 已上传字节数
  total: number         // 总字节数
}

// 上传结果类型
export interface UploadResult {
  success: boolean      // 是否成功
  url?: string          // 上传后的文件URL
  message?: string      // 消息
  data?: any           // 其他数据
}

// 上传配置类型
export interface UploadConfig {
  maxSize?: number      // 最大文件大小（字节）
  maxCount?: number     // 最大文件数量
  accept?: string       // 接受的文件类型
  multiple?: boolean    // 是否支持多选
  disabled?: boolean    // 是否禁用
  autoUpload?: boolean  // 是否自动上传
}

// 视频上传卡片组件的 Props 类型
export interface VideoUploadCardProps {
  modelValue: VideoFile[]
  title?: string
  subtitle?: string
  uploadText?: string
  uploadDesc?: string
  maxCount?: number
  disabled?: boolean
  showStats?: boolean
  isUploading?: boolean
  uploadProgress?: number
}

// 视频上传卡片组件的 Events 类型
export interface VideoUploadCardEvents {
  'update:modelValue': [value: VideoFile[]]
  'afterRead': [file: VideoFile]
  'delete': [file: VideoFile, index: number]
  'upload': []
  'preview': [file: VideoFile, index: number]
  'progress': [event: UploadProgressEvent]
  'success': [result: UploadResult]
  'error': [error: Error]
}
