<template>
    <view class="confirm-dialog" v-if="visible">
      <view class="dialog-mask"></view>
      <view class="dialog-content">
        <view class="dialog-header">
          <view class="header-icon" v-if="showIcon">
            <text class="icon" :style="{ color: iconColor }">{{ icon }}</text>
          </view>
          <text class="header-text">{{ headerText }}</text>
        </view>
        <view class="dialog-title">
          <view v-if="showFileInfo" class="file-info">
            <view class="file-name-container">
              <text class="file-label">文件</text>
              <text class="file-name" :title="fullFileName">{{ displayFileName }}</text>
            </view>
            <view class="file-warning">{{ warningText }}</view>
          </view>
          <view v-else>{{ title }}</view>
        </view>
        <view class="dialog-buttons">
          <button 
            class="button cancel-btn" 
            @click="handleCancel"
            hover-class="button-hover"
          >{{ cancelText }}</button>
          <button 
            class="button confirm-btn" 
            @click="handleConfirm"
            hover-class="button-hover"
            :style="{ background: color }"
          >{{ confirmText }}</button>
        </view>
      </view>
    </view>
</template>
  
<script setup>
import { defineProps, defineEmits } from 'vue'
  
const props = defineProps({
  visible: {
      type: Boolean,
      default: false
  },
  title: {
      type: String,
      default: '提示'
  },
  headerText: {
      type: String,
      default: '删除确认'
  },
  showIcon: {
      type: Boolean,
      default: false
  },
  icon: {
      type: String,
      default: '🗑️'
  },
  iconColor: {
      type: String,
      default: '#ff4757'
  },
  cancelText: {
      type: String,
      default: '取消'
  },
  confirmText: {
      type: String,
      default: '确认'
  },
  color: {
      type: String,
      default: '#1E88E5'
  },
  showFileInfo: {
      type: Boolean,
      default: false
  },
  fullFileName: {
      type: String,
      default: ''
  },
  displayFileName: {
      type: String,
      default: ''
  },
  warningText: {
      type: String,
      default: '删除后将无法恢复'
  }
})
  
const emit = defineEmits(['update:visible', 'confirm', 'cancel']);
    
const handleConfirm = () => {
  emit('update:visible', false);
  emit('confirm')
}
  
const handleCancel = () => {
  emit('cancel')
  emit('update:visible', false);
}
  </script>
  
<style lang="scss" scoped>
.confirm-dialog {
.dialog-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 999;
}

.dialog-content {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90vw;
    max-width: 560rpx;
    min-width: 320rpx;
    background: #FFFFFF;
    border-radius: 24rpx;
    z-index: 1000;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
    animation: dialogShow 0.3s ease-out;

    .dialog-header {
        padding: 24rpx 40rpx 12rpx;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16rpx;

        .header-icon {
          .icon {
            font-size: 56rpx;
            line-height: 1;
            filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
          }
        }

        .header-text {
          font-size: 32rpx;
          color: #333;
          font-weight: 600;
          letter-spacing: 1rpx;
        }
    }
    .dialog-title {
        padding: 12rpx 40rpx 24rpx;
        text-align: center;
        font-size: 28rpx;
        color: #666;
        line-height: 1.5;
        white-space: pre-line;
        word-wrap: break-word;
        word-break: break-word;
        max-height: 240rpx;
        overflow-y: auto;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        // 自定义滚动条样式
        &::-webkit-scrollbar {
          width: 6rpx;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 6rpx;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 6rpx;

          &:hover {
            background: #a8a8a8;
          }
        }

        // 文件信息样式
        .file-info {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;

          .file-question {
            font-size: 30rpx;
            color: #333;
            font-weight: 500;
            margin-bottom: 24rpx;
          }

          .file-name-container {
            background: #f8f9fa;
            padding: 16rpx 12rpx;
            border-radius: 10rpx;
            margin: 0 auto 16rpx;
            text-align: center;
            max-width: 100%;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .file-label {
              font-size: 24rpx;
              color: #666;
              display: block;
              margin-bottom: 6rpx;
              text-align: center;
            }

            .file-name {
              font-size: 26rpx;
              color: #333;
              font-weight: 500;
              word-break: break-word;
              line-height: 1.3;
              display: block;
              text-align: center;
              max-width: 100%;
            }
          }

          .file-warning {
            font-size: 24rpx;
            color: #ff4757;
            font-weight: 500;
            text-align: center;
          }
        }
    }
    .dialog-buttons {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 16rpx 40rpx;
      gap: 20rpx;
      margin-bottom: 16rpx;

      .button {
        flex: 1;
        max-width: 180rpx;
        height: 72rpx;
        line-height: 72rpx;
        text-align: center;
        font-size: 30rpx;
        border-radius: 10rpx;
        border: none;
        font-weight: 500;
        transition: all 0.2s ease;

        &.button-hover {
          opacity: 0.8;
          transform: translateY(-2rpx);
        }
      }

      .cancel-btn {
        background: #F5F5F5;
        color: #333333;
        border: 2rpx solid #E0E0E0;
      }

      .confirm-btn {
        color: #FFFFFF;
        box-shadow: 0 4rpx 12rpx rgba(30, 136, 229, 0.3);
      }
    }
}
}

// 动画效果
@keyframes dialogShow {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.dialog-mask {
  animation: maskShow 0.3s ease-out;
}

@keyframes maskShow {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
</style>