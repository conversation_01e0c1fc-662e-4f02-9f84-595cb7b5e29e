<template>
  <view class="page">
    <up-navbar
      @leftClick="goBack"
      :title="pageTitle"
      :safeAreaInsetTop="true"
      leftIcon="arrow-left"
      leftText="返回"
      leftIconColor="#1a1a1a"
      :border="false"
      :placeholder="true"
      titleStyle="color: #1a1a1a; font-weight: 600; font-size: 18px;"
    />
    <!-- 主要内容区域 -->
    <scroll-view class="container" scroll-y="true">
      <view class="card-content">
        <TemplateSelector v-model="template" placeholder="点击选择模版" />
      </view>

      <!-- 视频上传卡片 -->
      <VideoUploadCard
        v-model="videoList"
        :upload-text="getUploadText()"
        :max-count="getMaxMediaCount()"
        :disabled="isUploadDisabled"
        :is-uploading="isUploading"
        :upload-progress="uploadProgress"
        @after-read="afterRead"
        @delete="deletePic"
        @upload="handleUploadTrigger"
        @preview="handleVideoPreview"
      />

      <!-- 操作按钮 -->
      <view class="action-section">
        <up-button
          type="primary"
          size="large"
          @click="startSynthesis"
          :disabled="!canStartSynthesis"
          class="synthesis-btn"
        >
          <text class="btn-icon">✨</text>
          <text class="btn-text">开始合成</text>
        </up-button>
      </view>
    </scroll-view>

    <!-- 删除确认对话框 -->
    <ConfirmDialog
      v-model:visible="showDeleteDialog"
      :showIcon="true"
      iconColor="#ff4757"
      :showFileInfo="true"
      :fullFileName="deleteFileFullName"
      :displayFileName="deleteFileDisplayName"
      color="#ff4757"
      @confirm="handleConfirmDelete"
      @cancel="handleCancelDelete"
    />
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";

import modal from "@/plugins/modal";
import TemplateSelector from "@/components/platform/template-selector.vue";
import VideoUploadCard from "@/components/platform/video-upload-card.vue";
import ConfirmDialog from "@/components/platform/confirm-dialog.vue";

import { synthesizeVideo } from "@/api/platform/videoEdit";

const template = ref(null);
const videoList = ref([]);

// 上传相关状态
const isUploading = ref(false);
const uploadProgress = ref(0);

// 删除确认对话框相关状态
const showDeleteDialog = ref(false);
const deleteFileFullName = ref("");
const deleteFileDisplayName = ref("");
const pendingDeleteFile = ref(null);
const pendingDeleteIndex = ref(-1);

// 状态栏高度（APP端使用）
const statusBarHeight = ref(0);

// 页面参数
const pageParams = ref({
  from: "",
  projectId: "",
  title: "",
});

// 页面标题
const pageTitle = computed(() => {
  if (pageParams.value.projectId && pageParams.value.title) {
    return `编辑工程 - ${decodeURIComponent(pageParams.value.title)}`;
  }
  return "创建新工程";
});

// 是否可以开始合成
const canStartSynthesis = computed(() => {
  return template.value && videoList.value.length > 0;
});

// 获取页面参数
onMounted(() => {
  // 获取状态栏高度（APP端）
  // #ifdef APP-PLUS
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight || 0;
  // #endif

  // 获取页面参数
  try {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const options = currentPage?.options || {};

    pageParams.value = {
      from: options.from || "",
      projectId: options.projectId || "",
      title: options.title || "",
    };

    console.log("页面参数获取成功:", pageParams.value);
  } catch (error) {
    console.error("获取页面参数失败:", error);
    pageParams.value = {
      from: "",
      projectId: "",
      title: "",
    };
  }
});

// 计算是否禁用上传组件
const isUploadDisabled = computed(() => {
  return !template.value;
});

/**处理文件上传后的回调 */
const afterRead = (event) => {
  console.log("文件上传回调:", event);
  const { file } = event;
  const fileList = Array.isArray(file) ? file : [file];

  fileList.forEach((item) => {
    console.log("处理上传文件:", item);

    // 确保文件对象有必要的属性
    const videoFile = {
      ...item,
      // 确保有文件名
      name: item.name || `视频_${Date.now()}`,
      // 确保有文件大小
      size: item.size || 0,
      // 确保有文件类型
      type: item.type || "video/mp4",
      // 如果没有url但有path，可以使用path作为url
      url: item.url || item.path,
      // 保留原始路径
      path: item.path,
    };

    console.log("添加到视频列表:", videoFile);
    videoList.value.push(videoFile);
  });

  console.log("当前视频列表:", videoList.value);
};

// 格式化文件名，处理过长的文件名
const formatFileName = (fileName, maxLength = 30) => {
  if (!fileName) return "未命名视频";

  if (fileName.length <= maxLength) {
    return fileName;
  }

  // 如果文件名过长，显示前面部分...后面部分
  const start = fileName.substring(0, Math.floor(maxLength * 0.6));
  const end = fileName.substring(fileName.length - Math.floor(maxLength * 0.3));
  return `${start}...${end}`;
};

/**删除已上传的视频 */
const deletePic = (file, index) => {
  console.log("删除文件:", file, "索引:", index);
  console.log("当前视频列表:", videoList.value);

  // 保存待删除的文件信息
  pendingDeleteFile.value = file;
  pendingDeleteIndex.value = index;

  // 设置文件名信息
  const fileName = file.name || "未命名视频";
  deleteFileFullName.value = fileName;
  deleteFileDisplayName.value = formatFileName(fileName);

  // 显示确认对话框
  showDeleteDialog.value = true;
};

// 确认删除
const handleConfirmDelete = () => {
  const file = pendingDeleteFile.value;
  const index = pendingDeleteIndex.value;

  if (!file) {
    console.error("没有待删除的文件");
    return;
  }

  try {
    // 执行删除操作
    if (
      typeof index === "number" &&
      index >= 0 &&
      index < videoList.value.length
    ) {
      // 使用索引删除
      videoList.value.splice(index, 1);
      modal.msg("删除成功");
      console.log("删除成功，剩余视频:", videoList.value.length);
    } else {
      // 使用过滤删除（备用方案）
      const originalLength = videoList.value.length;
      videoList.value = videoList.value.filter((item) => {
        return !(
          (item.url && file.url && item.url === file.url) ||
          (item.path && file.path && item.path === file.path) ||
          (item.name &&
            file.name &&
            item.name === file.name &&
            item.size === file.size)
        );
      });

      if (videoList.value.length < originalLength) {
        modal.msg("删除成功");
        console.log("删除成功，剩余视频:", videoList.value.length);
      } else {
        modal.msg("删除失败：未找到匹配的文件");
        console.error("删除失败：未找到匹配的文件");
      }
    }

    // 这里可以添加删除服务器文件的逻辑
    // if (file.url) {
    //   await deleteVideoFromServer(file.url);
    // }
  } catch (e) {
    console.error("删除视频时出错:", e);
    modal.msg("删除失败");
  } finally {
    // 清理状态
    pendingDeleteFile.value = null;
    pendingDeleteIndex.value = -1;
    deleteFileFullName.value = "";
    deleteFileDisplayName.value = "";
  }
};

// 取消删除
const handleCancelDelete = () => {
  console.log("用户取消删除");
  // 清理状态
  pendingDeleteFile.value = null;
  pendingDeleteIndex.value = -1;
  deleteFileFullName.value = "";
  deleteFileDisplayName.value = "";
};

/**返回工作台 */
const navigateToWorkbench = () => {
  try {
    console.log("准备返回工作台");

    // 获取页面参数，判断来源
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const options = currentPage?.options || {};

    console.log("页面参数:", options);
    console.log("xixi", options.from);

    // 触发工作台列表刷新
    uni.$emit("refreshWorkList");

    // 如果是从工作台跳转过来的，直接返回；否则跳转到工作台
    if (options.from === "work") {
      console.lo("这个是什么");
      uni.navigateBack({
        fail: () => uni.switchTab({ url: "/pages/work" }),
      });
    } else {
      console.log("xxxx");
      uni.switchTab({ url: "/pages/work" });
    }
  } catch (error) {
    console.error("返回工作台操作失败:", error);
    // 发生错误时，尝试多种方式返回
    uni.navigateBack({
      fail: () => {
        // 如果返回失败，跳转到工作台
        uni.switchTab({
          url: "/pages/work",
        });
      },
    });
  }
};

/**页面返回按钮 */
const goBack = () => {
  navigateToWorkbench();
};

// 处理上传触发事件
const handleUploadTrigger = () => {
  console.log("触发上传");
};

// 处理视频预览
const handleVideoPreview = (file, index) => {
  console.log("预览视频:", file, index);
  console.log("文件详情:", {
    name: file.name,
    url: file.url,
    path: file.path,
    type: file.type,
    size: file.size,
  });

  // 获取视频URL，优先使用url，其次使用path
  const videoUrl = file.url || file.path;

  if (!videoUrl) {
    uni.showToast({
      title: "视频文件路径无效",
      icon: "none",
      duration: 2000,
    });
    return;
  }

  // 显示操作选择
  uni.showActionSheet({
    itemList: ["播放视频", "查看详情"],
    success: (res) => {
      if (res.tapIndex === 0) {
        // 播放视频
        playVideo(videoUrl, file);
      } else if (res.tapIndex === 1) {
        // 查看详情
        showVideoInfo(file);
      }
    },
  });
};

// 播放视频函数
const playVideo = (videoUrl, file) => {
  // #ifdef H5
  // H5环境：创建video元素播放
  if (typeof window !== "undefined") {
    // 创建一个临时的video元素
    const video = document.createElement("video");
    video.src = videoUrl;
    video.controls = true;
    video.style.width = "100%";
    video.style.height = "auto";
    video.style.maxWidth = "800px";
    video.style.maxHeight = "600px";

    // 创建模态框显示视频
    const modal = document.createElement("div");
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.8);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    `;

    const container = document.createElement("div");
    container.style.cssText = `
      position: relative;
      max-width: 90%;
      max-height: 90%;
    `;

    const closeBtn = document.createElement("button");
    closeBtn.innerHTML = "✕";
    closeBtn.style.cssText = `
      position: absolute;
      top: -40px;
      right: 0;
      background: #fff;
      border: none;
      border-radius: 50%;
      width: 30px;
      height: 30px;
      cursor: pointer;
      font-size: 16px;
    `;

    closeBtn.onclick = () => {
      document.body.removeChild(modal);
    };

    container.appendChild(video);
    container.appendChild(closeBtn);
    modal.appendChild(container);
    document.body.appendChild(modal);

    // 点击背景关闭
    modal.onclick = (e) => {
      if (e.target === modal) {
        document.body.removeChild(modal);
      }
    };
  }
  // #endif

  // #ifdef APP-PLUS
  // App环境：使用系统默认播放器
  if (typeof plus !== "undefined") {
    plus.runtime.openFile(videoUrl, {}, (error) => {
      console.error("打开视频失败:", error);
      uni.showModal({
        title: "播放失败",
        content: "无法播放此视频文件，可能是格式不支持或文件损坏",
        showCancel: false,
      });
    });
  }
  // #endif

  // #ifdef MP
  // 小程序环境：跳转到视频播放页面
  uni.navigateTo({
    url: `/pages_workbench/pages/videoEdit/video-player?url=${encodeURIComponent(
      videoUrl
    )}&name=${encodeURIComponent(file.name || "视频")}`,
  });
  // #endif
};

// 显示视频详情
const showVideoInfo = (file) => {
  const info = [
    `文件名: ${file.name || "未知"}`,
    `大小: ${formatFileSize(file.size)}`,
    `类型: ${file.type || "未知"}`,
    `路径: ${file.url || file.path || "无"}`,
  ].join("\n");

  uni.showModal({
    title: "视频详情",
    content: info,
    showCancel: false,
  });
};

// 格式化文件大小的辅助函数
const formatFileSize = (size) => {
  if (!size) return "0B";
  const units = ["B", "KB", "MB", "GB"];
  let index = 0;
  let fileSize = size;

  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024;
    index++;
  }

  return `${fileSize.toFixed(1)}${units[index]}`;
};

/**校验表单 */
const validateForm = () => {
  if (!template.value) {
    modal.msg("请选择模板");
    return false;
  }
  if (videoList.value.length == 0) {
    modal.msg("请选择视频");
    return false;
  }
  return true;
};

// 计算最大允许上传的媒体文件数量
const getMaxMediaCount = () => {
  if (!template.value || !template.value.ClipsParam) {
    return 0; // 默认最大数量
  }

  try {
    const clipsParam = JSON.parse(template.value.ClipsParam);
    console.log(
      "长度是什么>>",
      Object.values(clipsParam).filter((value) => value === "mediaId").length
    );

    return Object.values(clipsParam).filter((value) => value === "mediaId")
      .length;
  } catch (e) {
    return 0;
  }
};

// 获取上传按钮的显示文本
const getUploadText = () => {
  if (!template.value) {
    return "请先选择模板";
  }

  const maxCount = getMaxMediaCount();
  if (videoList.value.length >= maxCount) {
    return `已达到最大上传数量(${maxCount})`;
  }

  return "点击上传视频";
};

/**开始合成视频 */
const startSynthesis = async () => {
  if (!validateForm()) return;
  modal.loading("提交中...");
  try {
    const files = [];
    console.log("列表的数据是什么名", videoList.value);
    videoList.value.forEach((item) => {
      files.push({
        name: item.name,
        uri: item.url,
      });
    });
    console.log("files", files);
    console.log("template.value", template.value);
    const params = {
      templateId: template.value.TemplateId,
      clipsParam: template.value.ClipsParam,
      files: files,
    };
    console.log("params", params);
    const res = await synthesizeVideo(params);
    console.log("res", res);
    modal.closeLoading();
    // 显示成功提示并自动返回工作台
    uni.showToast({
      title: "合成成功",
      icon: "success",
      duration: 2000,
      success: () => {
        // 成功提示显示后自动返回工作台
        setTimeout(() => {
          navigateToWorkbench();
        }, 1500);
      },
    });
  } catch (e) {
    modal.closeLoading();
    console.error("合成失败:", e);

    // 显示更友好的错误信息
    const errorMessage =
      e?.message || e?.data?.message || "合成失败，请稍后重试";
    modal.msg(errorMessage);

    // 可以选择是否在失败时也提供返回选项
    setTimeout(() => {
      uni.showModal({
        title: "合成失败",
        content: "是否返回工作台？",
        confirmText: "返回",
        cancelText: "留在此页",
        success: (res) => {
          if (res.confirm) {
            navigateToWorkbench();
          }
        },
      });
    }, 2000);
  }
};
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  display: flex;
  flex-direction: column;

  .container {
    flex: 1;
    padding: 24rpx;

    // 卡片通用样式
    .card {
      background: #ffffff;
      border-radius: 24rpx;
      margin-bottom: 32rpx;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
      border: 1rpx solid rgba(0, 0, 0, 0.04);
      overflow: hidden;
      transition: all 0.3s ease;
      margin: 0 20rpx 32rpx;
      .card-header {
        padding: 32rpx 32rpx 24rpx;
        border-bottom: 1rpx solid #f1f5f9;

        .card-title {
          display: flex;
          align-items: center;
          margin-bottom: 12rpx;

          .title-icon {
            font-size: 32rpx;
            margin-right: 16rpx;
          }

          .title-text {
            font-size: 32rpx;
            font-weight: 600;
            color: #1e293b;
            line-height: 1.2;
          }
        }

        .card-subtitle {
          font-size: 26rpx;
          color: #64748b;
          line-height: 1.4;
        }
      }

      .card-content {
        padding: 32rpx;
      }
    }

    // 上传组件样式已移至 video-upload-card.vue 组件中

    // 操作按钮区域
    .action-section {
      padding: 40rpx 0;

      .synthesis-btn {
        width: 100%;
        height: 96rpx;
        border-radius: 20rpx;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.4);
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12rpx;
        transition: all 0.3s ease;

        &:active {
          transform: translateY(2rpx);
          box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.3);
        }

        &:disabled {
          background: #e2e8f0;
          box-shadow: none;

          .btn-text,
          .btn-icon {
            color: #94a3b8 !important;
          }
        }

        .btn-icon {
          font-size: 32rpx;
          color: #ffffff;
        }

        .btn-text {
          font-size: 32rpx;
          font-weight: 600;
          color: #ffffff;
        }
      }
    }
  }
}
</style>
