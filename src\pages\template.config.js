export default [
	{
		groupName: 'geek组件',
		groupName_en: 'Page',
		list: [
			{
				path: '/pages_geek/pages/index/index',
				icon: 'wxCenter',
				title: '组件展示',
				title_en: 'index',
			},
			{
				path: '/pages_geek/pages/code/index',
				icon: 'wxCenter',
				title: '二维码',
				title_en: 'index',
			}
		]
	},
	{
		groupName: '部件',
		groupName_en: 'Parts',
		list: [
			{
				path: '/pages_template/pages/coupon/index',
				icon: 'coupon',
				title: 'Coupon 优惠券',
				title_en: 'Coupon',
			},
			{
				path: '/pages_template/pages/citySelect/index',
				icon: 'citySelect',
				title: 'CitySelect 城市选择',
				title_en: 'CitySelect',
			},
			{
				path: '/pages_template/pages/submitBar/index',
				icon: 'submitBar',
				title: 'SubmitBar 提交订单栏',
				title_en: 'SubmitBar',
			},
			{
				path: '/pages_template/pages/keyboardPay/index',
				icon: 'keyboardPay',
				title: 'KeyboardPay 自定义键盘支付模板',
				title_en: 'KeyboardPay',
			},
		]
	},
	{
		groupName: '报表',
		groupName_en: 'Parts',
		list: [
			{
				path: '/pages_qiun/pages/finance/index',
				icon: 'coupon',
				title: '财务报告',
				title_en: 'finace',
			},
			{
				path: '/pages_qiun/pages/main/index',
				icon: 'coupon',
				title: '数据报表中心',
				title_en: 'main',
			},
			{
				path: '/pages_qiun/pages/school/index',
				icon: 'coupon',
				title: '智慧教育报表中心',
				title_en: 'school',
			},
			{
				path: '/pages_qiun/pages/sport/index',
				icon: 'coupon',
				title: '运动报告',
				title_en: 'sport',
			},
		]
	},
	{
		groupName: '页面',
		groupName_en: 'Page',
		list: [
			{
				path: '/pages_template/pages/wxCenter/index',
				icon: 'wxCenter',
				title: 'WxCenter 仿微信个人中心',
				title_en: 'WxCenter',
			},
			{
				path: '/pages_template/pages/mallMenu/index1',
				icon: 'mall_menu_1',
				title: 'MallMenu 垂直分类(左右独立)',
				title_en: 'MallMenu 1',
			}, {
				path: '/pages_template/pages/mallMenu/index2',
				icon: 'mall_menu_2',
				title: 'MallMenu 垂直分类(左右联动)',
				title_en: 'MallMenu 2',
			}, {
				path: '/pages_template/pages/comment/index',
				icon: 'comment',
				title: 'Comment 评论列表',
				title_en: 'Comment',
			}, {
				path: '/pages_template/pages/order/index',
				icon: 'order',
				title: 'Order 订单列表',
				title_en: 'Order',
			},
			{
				path: '/pages_template/pages/login/index1',
				icon: 'login',
				title: 'Login 登录界面',
				title_en: 'Login',
			},
			{
				path: '/pages_template/pages/login/index2',
				icon: 'login',
				title: 'Login 水滴登录',
				title_en: 'Login',
			},
			{
				path: '/pages_template/pages/address/index',
				icon: 'address',
				title: 'Address 收货地址',
				title_en: 'Address',
			},
		]
	},
]