{"exclude": ["node_modules"], "compilerOptions": {"noImplicitAny": true, "importHelpers": true, "strict": true, "forceConsistentCasingInFileNames": true, "downlevelIteration": true, "outDir": "dist/compiled", "baseUrl": "./", "allowSyntheticDefaultImports": true, "allowJs": true, "jsx": "preserve", "moduleResolution": "node", "module": "ES2022", "lib": ["ES2020", "dom"], "declaration": true, "paths": {"@": ["src"], "@/*": ["src/*"], "/@/*": ["src/*"], "vue": ["node_modules/vue"], "tslib": ["node_modules/tslib/tslib.d.ts"]}}}