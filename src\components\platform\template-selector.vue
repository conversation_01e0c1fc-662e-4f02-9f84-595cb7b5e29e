<template>
  <view class="template-selector">
    <!-- 触发按钮 -->
    <NavItem
      title="选择模版"
      :value="selectedTemplate ? selectedTemplate.Name : ''"
      :placeholder="placeholder"
      @click="openTemplateSelector"
    />

    <!-- ActionSheet 弹出菜单 -->
    <u-action-sheet
      :show="showActionSheet"
      @close="closeActionSheet"
      cancelText="取消"
      round="true"
    >
      <view class="custom-content" v-if="templates.length > 0">
        <scroll-view class="template-scroll" scroll-y="true">
          <view class="template-list">
            <view
              class="template-item"
              v-for="(template, index) in templates"
              :key="template.TemplateId"
              @click="selectTemplate(template, index)"
            >
              <image
                class="template-image"
                :src="template.imageAddress"
                mode="aspectFill"
              />
              <view class="template-info">
                <text class="template-name">{{ template.Name }}</text>
                <text class="template-desc">{{ template.Status }}</text>
                <text class="template-source">{{ template.CreateSource }}</text>
                <text class="template-time">{{ template.CreationTime }}</text>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
      <view v-else class="empty-template">
        <text class="empty-text">暂无模板数据</text>
      </view>
    </u-action-sheet>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import NavItem from "@/components/platform/nav-item.vue";
import { listTemplates } from "@/api/platform/template";

// 定义属性
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => null,
  },
  placeholder: {
    type: String,
    default: "点击选择模版",
  },
});

// 定义事件
const emit = defineEmits(["update:modelValue"]);

// 数据状态
const showActionSheet = ref(false);
const templates = ref([]);
const selectedTemplate = computed(() => props.modelValue);

// 获取模板列表
const getTemplateList = async () => {
  try {
    const params = {
      pageNum: 1,
      pageSize: 999,
    };
    const res = await listTemplates(params);
    console.log("res", res);
    if (res.code === 200) {
      templates.value = res.data.Templates || [];
    }
    console.log("templates", templates);
  } catch (err) {
    console.error("获取模板列表失败:", err);
  }
};

// 打开模板选择器
const openTemplateSelector = () => {
  getTemplateList();
  showActionSheet.value = true;
};

// 关闭模板选择器
const closeActionSheet = () => {
  showActionSheet.value = false;
};

// 直接选择模板（通过点击列表项）
const selectTemplate = (template) => {
  emit("update:modelValue", template);
  closeActionSheet();
};

// 组件挂载时获取模板列表
onMounted(() => {
  getTemplateList();
});
</script>

<style lang="scss" scoped>
.template-selector {
  .custom-content {
    padding: 0;
    max-height: 70vh;

    .template-scroll {
      max-height: 60vh;

      .template-list {
        padding: 20rpx;
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300rpx, 1fr));
        gap: 20rpx;

        .template-item {
          display: flex;
          flex-direction: column;
          padding: 20rpx;
          border-radius: 16rpx;
          background-color: #fff;
          box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
          transition: all 0.3s ease;
          overflow: hidden;

          /*  #ifdef  MP-WEIXIN  */
          background-color: #2c2c2c;
          box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.2);
          /*  #endif  */

          &:active {
            transform: translateY(-4rpx);
            box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);

            /*  #ifdef  MP-WEIXIN  */
            background-color: #3d3d3d;
            /*  #endif  */
          }

          .template-image {
            width: 100%;
            height: 200rpx;
            border-radius: 12rpx;
            margin-bottom: 16rpx;
            background-color: #f5f5f5;
            object-fit: cover;

            /*  #ifdef  MP-WEIXIN  */
            background-color: #444;
            /*  #endif  */
          }

          .template-info {
            flex: 1;
            display: flex;
            flex-direction: column;

            .template-name {
              font-size: 28rpx;
              font-weight: bold;
              color: #333;
              display: block;
              margin-bottom: 8rpx;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;

              /*  #ifdef  MP-WEIXIN  */
              color: #e3e2e7;
              /*  #endif  */
            }

            .template-desc {
              font-size: 24rpx;
              color: #666;
              margin-bottom: 8rpx;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;

              /*  #ifdef  MP-WEIXIN  */
              color: #aaa;
              /*  #endif  */
            }

            .template-source,
            .template-time {
              font-size: 22rpx;
              color: #999;
              line-height: 1.4;

              /*  #ifdef  MP-WEIXIN  */
              color: #888;
              /*  #endif  */
            }
          }
        }
      }
    }
  }

  .empty-template {
    padding: 60rpx 0;
    text-align: center;

    .empty-text {
      font-size: 28rpx;
      color: #999;
    }
  }
}
</style>
