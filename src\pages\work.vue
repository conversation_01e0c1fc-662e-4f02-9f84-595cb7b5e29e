<template>
  <view class="video-edit-container">
    <!-- 顶部标题区域 -->
    <view class="page-header">
      <view class="header-content">
        <view class="title-section">
          <view class="title-icon">
            <text class="iconfont icon-video"></text>
          </view>
          <view class="title-text">
            <text class="main-title">云剪辑工作台</text>
            <text class="sub-title">智能视频编辑，让创作更简单</text>
          </view>
        </view>
        <view class="header-actions">
          <up-button
            type="primary"
            size="normal"
            @click="handleCreate"
            class="create-btn"
          >
            <text class="btn-icon">➕</text>
            <text>创建新工程</text>
          </up-button>
        </view>
      </view>
    </view>

    <!-- 搜索和筛选区域 -->
    <SimpleSearchFilter
      v-model:activeFilter="queryParams.status"
      v-model:startTime="queryParams.startTime"
      v-model:endTime="queryParams.endTime"
      :filter-tags="statusOptions"
      :show-result-count="true"
      :result-count="taskList.length"
      :show-time-filter="true"
      @search="handleSearch"
      @clear-all="handleClearAll"
    />

    <!-- 项目列表组件 -->
    <view class="project-list-wrapper">
      <ProjectList
        :project-list="taskList"
        :loading="loading"
        @item-click="handleItemClick"
        @edit="handleEdit"
        @delete="handleDelete"
        @create="handleCreate"
      />
    </view>

    <up-loadmore
      :status="status"
      :loading-text="loadingText"
      :loadmore-text="loadmoreText"
      :nomore-text="nomoreText"
      dashed
      line
      v-if="taskList.length > 0 && !loading"
    />

    <!-- 删除确认对话框 -->
    <ConfirmDialog
      v-model:visible="showDeleteDialog"
      :title="deleteDialogTitle"
      headerText="删除确认"
      :showIcon="true"
      icon="🗑️"
      iconColor="#ff4757"
      confirmText="删除"
      cancelText="取消"
      color="#ff4757"
      warningText="删除后无法恢复"
      @confirm="handleConfirmDelete"
      @cancel="handleCancelDelete"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from "vue";
import {
  type ProjectInfo,
  listEditingProjects,
  deleteEditingProjects,
} from "@/api/platform/videoEdit";
import { onReachBottom, onPullDownRefresh , onLoad} from "@dcloudio/uni-app";
import ProjectList from "@/components/platform/project-list.vue";
import SimpleSearchFilter from "@/components/platform/simple-search-filter.vue";
import ConfirmDialog from "@/components/platform/confirm-dialog.vue";
import modal from "@/plugins/modal";

const status = ref("loadmore");
const loadmoreText = ref("轻轻上拉");
const loadingText = ref("努力加载中");
const nomoreText = ref("实在没有了");

// 响应式数据
const loading = ref(false);
const taskList = ref<ProjectInfo[]>([]);
const nextToken = ref<string | undefined>(undefined);
const queryParams = ref({
  keyword: "",
  status: "" as string | undefined,
  startTime: "",
  endTime: "",
  maxResults: 10,
});

//对话框的数据
const showDeleteDialog = ref(false);
const pendingDeleteItem = ref<ProjectInfo | null>(null);
const deleteDialogTitle = computed(() => {
  if (!pendingDeleteItem.value) return "确定要删除该工程吗？";
  const title = pendingDeleteItem.value.Title || "未命名工程";
  return `确定要删除工程"${title}"吗？`;
});
onLoaded(() => { 
});
// 页面加载时获取数据
onMounted(() => {
  getTaskList(true);
});

// 获取项目列表
const getTaskList = async (isRefresh = false) => {
  try {
    // 如果是刷新，显示加载状态并重置token
    if (isRefresh) {
      loading.value = true;
      nextToken.value = undefined;
      status.value = "loadmore";
    } else {
      status.value = "loading";
    }

    // 构建请求参数
    const params = {
      ...queryParams.value,
      nextToken: nextToken.value,
    };
    console.log("请求参数:", params);
    const res = await listEditingProjects(params);
    console.log("获取到的列表数据:", res);

    const responseData = res.data;
    const allProjects = responseData.ProjectList || [];

    if (isRefresh || !nextToken.value) {
      // 第一页或刷新，直接替换数据
      taskList.value = allProjects;
    } else {
      // 后续页，追加数据
      taskList.value.push(...allProjects);
    }

    // 更新分页信息
    nextToken.value = responseData.NextToken;

    // 判断是否还有更多数据
    if (!responseData.NextToken || allProjects.length === 0) {
      status.value = "nomore";
    } else {
      status.value = "loadmore";
    }
  } catch (error) {
    console.log("出现错误:", error);
    modal.msg("加载失败");
    status.value = "loadmore";
  } finally {
    loading.value = false;
    console.log("获取列表完成");
    uni.stopPullDownRefresh();
  }
};

/**下拉刷新 */
onPullDownRefresh(() => {
  console.log("触发下拉刷新");
  taskList.value = [];
  getTaskList(true);
});

/** 上拉加载更多 */
onReachBottom(() => {
  console.log("触发上拉加载，当前状态:", status.value);
  if (status.value === "loadmore" && !loading.value && nextToken.value) {
    console.log("开始加载下一页，nextToken:", nextToken.value);
    getTaskList(false);
  }
});

// 状态选项
const statusOptions = computed(() => [
  { label: "全部", value: "" },
  { label: "草稿", value: "Draft" },
  { label: "编辑中", value: "Editing" },
  { label: "制作中", value: "Producing" },
  { label: "已完成", value: "Produced" },
  { label: "失败", value: "ProduceFailed" },
]);

// 搜索处理函数
const handleSearch = (searchData: any) => {
  console.log("搜索数据:", searchData);

  // 将搜索数据赋值给查询参数
  if (searchData) {
    queryParams.value.keyword = searchData.keyword || "";
    queryParams.value.status = searchData.filter || "";
    queryParams.value.startTime = searchData.startTime || "";
    queryParams.value.endTime = searchData.endTime || "";
  }

  console.log("更新后的查询参数:", queryParams.value);

  // 触发列表刷新
  getTaskList(true);
};

// 清空所有筛选
const handleClearAll = () => {
  console.log("清空所有筛选");
  queryParams.value.keyword = "";
  queryParams.value.status = "";
  queryParams.value.startTime = "";
  queryParams.value.endTime = "";
  // 触发列表刷新
  getTaskList(true);
};

// 项目列表项点击事件
function handleItemClick(item: ProjectInfo) {
  console.log("点击项目:", item);
}

// 创建工程 - 跳转到视频剪辑页面
function handleCreate() {
  uni.navigateTo({
    url: "/pages_workbench/pages/videoEdit/index?from=work",
    success: (res) => {
      console.log("跳转成功:", res);
    },
    fail: (err) => {
      modal.msg(`跳转失败: ${err.errMsg}`);
    },
  });
}

// 编辑工程 - 跳转到视频剪辑页面
function handleEdit(item: ProjectInfo) {
  uni.navigateTo({
    url: `/pages/index?from=work&projectId=${
      item.ProjectId
    }&title=${encodeURIComponent(item.Title)}`,
  });
}

/**删除方法 */
function handleDelete(item: ProjectInfo) {
  pendingDeleteItem.value = item;
  showDeleteDialog.value = true;
}

/**确认删除方法 */
async function handleConfirmDelete() {
  if (!pendingDeleteItem.value) return;

  try {
    uni.showLoading({
      title: "删除中...",
    });

    await deleteEditingProjects([pendingDeleteItem.value.ProjectId]);

    uni.hideLoading();

    modal.msg("删除成功");

    getTaskList(true);
  } catch (error) {
    uni.hideLoading();
    console.error("删除失败:", error);
    uni.showToast({
      title: "删除失败",
      icon: "error",
    });
  } finally {
    // 清理状态
    pendingDeleteItem.value = null;
    showDeleteDialog.value = false;
  }
}

function handleCancelDelete() {
  pendingDeleteItem.value = null;
  showDeleteDialog.value = false;
}

// 页面卸载时移除监听
onUnmounted(() => {
  uni.$off("refreshWorkList");
});
</script>

<style lang="scss" scoped>
.video-edit-container {
  min-height: 100vh;
  background-color: #f5f5f5;

  // 顶部标题区域
  .page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20rpx 30rpx;
    padding-top: calc(var(--status-bar-height) + 20rpx);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-section {
        display: flex;
        align-items: center;

        .title-icon {
          width: 60rpx;
          height: 60rpx;
          background-color: rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20rpx;

          .iconfont {
            font-size: 32rpx;
            color: #fff;

            &.icon-video:before {
              content: "🎬";
            }
          }
        }

        .title-text {
          display: flex;
          flex-direction: column;

          .main-title {
            font-size: 36rpx;
            font-weight: bold;
            color: #fff;
            line-height: 1.2;
          }

          .sub-title {
            font-size: 24rpx;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 4rpx;
          }
        }
      }

      .header-actions {
        .create-btn {
          background: rgba(255, 255, 255, 0.2) !important;
          border: 1rpx solid rgba(255, 255, 255, 0.3) !important;
          border-radius: 50rpx !important;
          padding: 16rpx 24rpx !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;

          .btn-icon {
            font-size: 24rpx;
            color: #fff;
            margin-right: 8rpx;
          }

          text {
            color: #fff;
            font-size: 28rpx;
          }
        }
      }
    }
  }

  // 项目列表包装器
  .project-list-wrapper {
    padding: 20rpx;
  }
}
</style>
