<template>
    <button 
      class="primary-button" 
      :disabled="disabled"
      :loading="loading"
      @click="handleClick"
    >
      <slot>{{ text }}</slot>
    </button>
  </template>
  
  <script setup>
  defineProps({
    // 按钮文字
    text: {
      type: String,
      default: ''
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否显示加载状态
    loading: {
      type: Boolean,
      default: false
    }
  })
  
  // 定义事件
  const emit = defineEmits(['click'])
  
  const handleClick = (e) => {
    emit('click', e)
  }
  </script>
  
  <style lang="scss" scoped>
  .primary-button {
    margin: 0 20rpx;
    height: 88rpx;
    line-height: 88rpx;
    background-color: #1e88e5 !important;
    color: #fff !important;
    border-radius: 8rpx;
    font-size: 32rpx;
    border: none;
  
    &:disabled {
      background: #ccc;
    }
  
    &.button-hover {
      opacity: 0.9;
    }
  }
  </style>