import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'

export default defineConfig(() => {
    return {
        base: './',
        build: {
            minify: true,
            outDir: 'dist',
        },
        server: {
            port: '80',
            open: true,
            proxy: {
                '/api': {
                    target: 'http://192.168.110.49:8899',
                    changeOrigin: true,
                    rewrite: (path) => path.replace(/^\/api/, '')
                },
                '/prod-api': {
                    target: 'https://118.190.145.116/prod-api/',
                    changeOrigin: true,
                    rewrite: (path) => path.replace(/^\/prod-api/, '')
                }
            }
        },
        plugins: [
            uni()
        ],
        exclude: [
            /\/README\.md$/,
        ],
        css: {
            preprocessorOptions: {
                scss: {
                    api: 'modern-compiler'
                }
            }
        }
    }
})