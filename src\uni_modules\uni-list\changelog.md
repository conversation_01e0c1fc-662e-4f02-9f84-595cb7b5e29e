## 1.2.1（2022-03-30）
- 删除无用文件
## 1.2.0（2021-11-23）
- 优化 组件UI，并提供设计资源，详见:[https://uniapp.dcloud.io/component/uniui/resource](https://uniapp.dcloud.io/component/uniui/resource)
- 文档迁移，详见:[https://uniapp.dcloud.io/component/uniui/uni-list](https://uniapp.dcloud.io/component/uniui/uni-list)
## 1.1.3（2021-08-30）
- 修复 在vue3中to属性在发行应用的时候报错的bug
## 1.1.2（2021-07-30）
- 优化 vue3下事件警告的问题
## 1.1.1（2021-07-21）
- 修复 与其他组件嵌套使用时，点击失效的Bug
## 1.1.0（2021-07-13）
- 组件兼容 vue3，如何创建vue3项目，详见 [uni-app 项目支持 vue3 介绍](https://ask.dcloud.net.cn/article/37834)
## 1.0.17（2021-05-12）
- 新增 组件示例地址
## 1.0.16（2021-02-05）
- 优化 组件引用关系，通过uni_modules引用组件
## 1.0.15（2021-02-05）
- 调整为uni_modules目录规范
- 修复 uni-list-chat 角标显示不正常的问题
