# 视频剪辑工作台功能说明

## 功能概述

本项目实现了一个完整的视频剪辑工作台，包含以下主要功能：

1. **云剪辑工作台** (`pages/work.vue`) - 视频合成任务列表管理
2. **视频剪辑界面** (`pages/index.vue`) - 视频上传和合成功能

## 页面流程

### 1. 工作台页面 (pages/work.vue)

**主要功能：**
- 显示视频合成任务列表
- 支持分页加载（每页10条数据）
- 支持上下滑动刷新和加载更多
- 支持搜索和状态筛选
- 支持删除工程
- 点击"创建新工程"跳转到视频剪辑页面
- 点击"编辑"按钮跳转到视频剪辑页面

**分页加载特性：**
- 初始加载10条数据
- 下拉刷新重新加载
- 上拉加载更多数据
- 显示加载状态和"没有更多数据"提示

**页面布局：**
- 顶部：渐变色标题区域，包含标题和"创建新工程"按钮
- 中间：搜索框和状态筛选
- 底部：项目列表，每个项目显示预览图、标题、描述、状态和操作按钮

### 2. 视频剪辑页面 (pages/index.vue)

**主要功能：**
- 选择模板
- 上传视频文件
- 开始视频合成
- 根据来源页面显示不同标题

**页面标题逻辑：**
- 新建工程：显示"创建新工程"
- 编辑工程：显示"编辑工程 - [工程名称]"

**返回逻辑：**
- 如果从工作台跳转过来，返回时会刷新工作台列表
- 合成成功后自动返回工作台

## 技术实现

### API接口

在 `src/api/platform/template.ts` 中新增了以下接口：

```typescript
// 获取剪辑工程列表
listEditingProjects(params: VideoEditListParams)

// 创建剪辑工程  
createEditingProject(data: CreateEditingProjectDTO)

// 删除剪辑工程
deleteEditingProjects(projectIds: string[])
```

### 类型定义

```typescript
interface ProjectInfo {
  ProjectId: string
  Title: string
  Description?: string
  Status: string
  CoverURL?: string
  CreateTime: string
  ModifiedTime: string
  Timeline?: any
}

interface VideoEditListParams {
  pageNum?: number
  pageSize?: number
  keyword?: string
  status?: string
  maxResults?: number
}
```

### 状态管理

工程状态包括：
- Draft: 草稿
- Editing: 编辑中
- Producing: 制作中
- Produced: 已制作完成
- ProduceFailed: 制作失败
- Normal: 正常

### 页面跳转参数

从工作台跳转到视频剪辑页面时传递的参数：
- `from=work`: 标识来源页面
- `projectId`: 工程ID（编辑时）
- `title`: 工程标题（编辑时）

## 使用方式

1. **查看工程列表**
   - 进入工作台页面，查看所有视频合成任务
   - 支持搜索和筛选功能

2. **创建新工程**
   - 点击"创建新工程"按钮
   - 跳转到视频剪辑页面
   - 选择模板，上传视频，开始合成

3. **编辑现有工程**
   - 在工程列表中点击"编辑"按钮
   - 跳转到视频剪辑页面，标题显示工程名称

4. **删除工程**
   - 在工程列表中点击"删除"按钮
   - 确认后删除工程

## 样式特色

- 使用渐变色背景的现代化设计
- 卡片式布局，支持点击交互效果
- 响应式设计，适配不同屏幕尺寸
- 加载状态和空状态的友好提示
- 状态标签使用不同颜色区分

## 注意事项

1. 页面使用了自定义导航栏，需要在 `pages.json` 中配置 `"navigationStyle": "custom"`
2. 分页加载是前端实现的，实际项目中建议使用后端分页
3. 页面间的数据刷新使用了 uni.$emit 和 uni.$on 事件机制
4. 时间格式化使用了项目中的 `parseTime` 工具函数
